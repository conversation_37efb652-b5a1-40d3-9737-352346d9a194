const BaseController = require('./BaseController');
const AuthService = require('../services/authService');
const LoginInput = require('../inputs/LoginInput');
const RegisterInput = require('../inputs/RegisterInput');
const { AuthOutput, TokenVerificationOutput, LogoutOutput } = require('../outputs/authResponses');

class AuthController extends BaseController {
  /**
   * Handle user login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static login = this.createMethod(async (req, res) => {
    // Validate input
    const input = LoginInput.new(req.body);
    if (!this.validateInput(input, res)) {
      return;
    }

    // Authenticate user using validated input
    const result = await AuthService.authenticateUser(input.output.email, input.output.password);

    // Success response using AuthOutput with login format
    const output = AuthOutput.login(result, { status: 200 });
    this.sendResponse(output, res);
  });

  /**
   * Handle user registration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static register = this.createMethod(async (req, res) => {
    // Validate input
    const input = RegisterInput.new(req.body);
    if (!this.validateInput(input, res)) {
      return;
    }

    // Create user using validated input
    const result = await AuthService.createUser(input.output);

    // Success response using AuthOutput with register format
    const output = AuthOutput.register(result, { status: 201 });
    this.sendResponse(output, res);
  });

  /**
   * Handle token verification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static verifyToken = this.createMethod(async (req, res) => {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      const error = new Error('No token provided');
      error.statusCode = 401;
      throw error;
    }

    const decoded = AuthService.verifyToken(token);
    const output = new TokenVerificationOutput(decoded);
    this.sendResponse(output, res);
  });

  /**
   * Handle user logout (for future use - token blacklisting)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static logout = this.createMethod(async (_req, res) => {
    // In a real application, you might want to blacklist the token
    // For now, we'll just return a success message
    const output = new LogoutOutput();
    this.sendResponse(output, res);
  });
}

module.exports = AuthController;
