const jwt = require('jsonwebtoken');
const User = require('../models/User');

class AuthService {
  /**
   * Generate JWT token for user
   * @param {Object} user - User object
   * @returns {string} JWT token
   */
  static generateToken(user) {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    const options = {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      issuer: 'simple-express-api',
      audience: 'simple-express-api-users',
    };

    return jwt.sign(payload, process.env.JWT_SECRET, options);
  }

  /**
   * Verify JWT token
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   */
  static verifyToken(token) {
    const options = {
      issuer: 'simple-express-api',
      audience: 'simple-express-api-users',
    };

    return jwt.verify(token, process.env.JWT_SECRET, options);
  }

  /**
   * Authenticate user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} Authentication result
   */
  static async authenticateUser(email, password) {
    // Find user by email
    const user = await User.findByEmail(email);

    if (!user) {
      const error = new Error('Invalid email or password');
      error.statusCode = 401;
      throw error;
    }

    // Verify password
    const isPasswordValid = await user.verifyPassword(password);

    if (!isPasswordValid) {
      const error = new Error('Invalid email or password');
      error.statusCode = 401;
      throw error;
    }

    // Generate token
    const token = this.generateToken(user);

    // Return the data directly (no success wrapper)
    return {
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        created_at: user.created_at,
      },
    };
  }

  /**
   * Create a new user (for future registration endpoint)
   * @param {Object} userData - User data
   * @returns {Object} User creation result
   */
  static async createUser(userData) {
    const { name, email, password, role = 'user' } = userData;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      const error = new Error('User with this email already exists');
      error.statusCode = 409;
      throw error;
    }

    // Hash password
    const password_digest = await User.hashPassword(password);

    // Create user
    const user = await User.create({
      name,
      email,
      password_digest,
      role,
    });

    // Generate token
    const token = this.generateToken(user);

    // Return the data directly (no success wrapper)
    return {
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        created_at: user.created_at,
      },
    };
  }
}

module.exports = AuthService;
